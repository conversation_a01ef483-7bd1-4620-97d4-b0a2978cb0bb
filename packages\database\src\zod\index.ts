import { z } from 'zod';
import { Prisma } from '@prisma/client';
import Decimal from 'decimal.js';

/////////////////////////////////////////
// HELPER FUNCTIONS
/////////////////////////////////////////

// JSON
//------------------------------------------------------

export type NullableJsonInput = Prisma.JsonValue | null | 'JsonNull' | 'DbNull' | Prisma.NullTypes.DbNull | Prisma.NullTypes.JsonNull;

export const transformJsonNull = (v?: NullableJsonInput) => {
  if (!v || v === 'DbNull') return Prisma.DbNull;
  if (v === 'JsonNull') return Prisma.JsonNull;
  return v;
};

export const JsonValueSchema: z.ZodType<Prisma.JsonValue> = z.lazy(() =>
  z.union([
    z.string(),
    z.number(),
    z.boolean(),
    z.literal(null),
    z.record(z.lazy(() => JsonValueSchema.optional())),
    z.array(z.lazy(() => JsonValueSchema)),
  ])
);

export type JsonValueType = z.infer<typeof JsonValueSchema>;

export const NullableJsonValue = z
  .union([JsonValueSchema, z.literal('DbNull'), z.literal('JsonNull')])
  .nullable()
  .transform((v) => transformJsonNull(v));

export type NullableJsonValueType = z.infer<typeof NullableJsonValue>;

export const InputJsonValueSchema: z.ZodType<Prisma.InputJsonValue> = z.lazy(() =>
  z.union([
    z.string(),
    z.number(),
    z.boolean(),
    z.object({ toJSON: z.function(z.tuple([]), z.any()) }),
    z.record(z.lazy(() => z.union([InputJsonValueSchema, z.literal(null)]))),
    z.array(z.lazy(() => z.union([InputJsonValueSchema, z.literal(null)]))),
  ])
);

export type InputJsonValueType = z.infer<typeof InputJsonValueSchema>;

// DECIMAL
//------------------------------------------------------

export const DecimalJsLikeSchema: z.ZodType<Prisma.DecimalJsLike> = z.object({
  d: z.array(z.number()),
  e: z.number(),
  s: z.number(),
  toFixed: z.function(z.tuple([]), z.string()),
})

export const DECIMAL_STRING_REGEX = /^(?:-?Infinity|NaN|-?(?:0[bB][01]+(?:\.[01]+)?(?:[pP][-+]?\d+)?|0[oO][0-7]+(?:\.[0-7]+)?(?:[pP][-+]?\d+)?|0[xX][\da-fA-F]+(?:\.[\da-fA-F]+)?(?:[pP][-+]?\d+)?|(?:\d+|\d*\.\d+)(?:[eE][-+]?\d+)?))$/;

export const isValidDecimalInput =
  (v?: null | string | number | Prisma.DecimalJsLike): v is string | number | Prisma.DecimalJsLike => {
    if (v === undefined || v === null) return false;
    return (
      (typeof v === 'object' && 'd' in v && 'e' in v && 's' in v && 'toFixed' in v) ||
      (typeof v === 'string' && DECIMAL_STRING_REGEX.test(v)) ||
      typeof v === 'number'
    )
  };

/////////////////////////////////////////
// ENUMS
/////////////////////////////////////////

export const TransactionIsolationLevelSchema = z.enum(['ReadUncommitted','ReadCommitted','RepeatableRead','Serializable']);

export const UserScalarFieldEnumSchema = z.enum(['id','name','email','emailVerified','image','createdAt','updatedAt','username','role','banned','banReason','banExpires','onboardingComplete','paymentsCustomerId','locale']);

export const SessionScalarFieldEnumSchema = z.enum(['id','expiresAt','ipAddress','userAgent','userId','impersonatedBy','activeOrganizationId','token','createdAt','updatedAt']);

export const AccountScalarFieldEnumSchema = z.enum(['id','accountId','providerId','userId','accessToken','refreshToken','idToken','expiresAt','password','accessTokenExpiresAt','refreshTokenExpiresAt','scope','createdAt','updatedAt']);

export const VerificationScalarFieldEnumSchema = z.enum(['id','identifier','value','expiresAt','createdAt','updatedAt']);

export const PasskeyScalarFieldEnumSchema = z.enum(['id','name','publicKey','userId','credentialID','counter','deviceType','backedUp','transports','createdAt']);

export const OrganizationScalarFieldEnumSchema = z.enum(['id','name','slug','logo','createdAt','metadata','paymentsCustomerId']);

export const MemberScalarFieldEnumSchema = z.enum(['id','organizationId','userId','role','createdAt']);

export const InvitationScalarFieldEnumSchema = z.enum(['id','organizationId','email','role','status','expiresAt','inviterId']);

export const PurchaseScalarFieldEnumSchema = z.enum(['id','organizationId','userId','type','customerId','subscriptionId','productId','status','createdAt','updatedAt']);

export const AiChatScalarFieldEnumSchema = z.enum(['id','organizationId','userId','title','messages','createdAt','updatedAt']);

export const ShareholderRegistryScalarFieldEnumSchema = z.enum(['id','fileName','recordCount','registerDate','companyCode','organizationId','userId','uploadedAt']);

export const CompanyInfoScalarFieldEnumSchema = z.enum(['id','registryId','organizationId','companyCode','companyName','registerDate','totalShareholders','totalInstitutions','largeShareholdersCount','largeSharesCount','totalShares','institutionShares','marginAccounts','marginShares','uploadedAt']);

export const ShareholderScalarFieldEnumSchema = z.enum(['id','shareholderId','registryId','organizationId','unifiedAccountNumber','securitiesAccountName','shareholderCategory','numberOfShares','lockedUpShares','shareholdingRatio','frozenShares','cashAccount','sharesInCashAccount','marginAccount','sharesInMarginAccount','contactAddress','contactNumber','zipCode','relatedPartyIndicator','clientCategory','marginCollateralAccountNumber','marginCollateralAccountName','natureOfShares','shareTradingCategory','rightsCategory','remarks','shareholderType','registerDate','uploadedAt']);

export const ShareholderClassificationRuleScalarFieldEnumSchema = z.enum(['id','priority','type','rule','matchField','updatedAt']);

export const CompanyFilterScalarFieldEnumSchema = z.enum(['id','organizationId','companyCode','benchmarkCompanyCodes','modifiedAt']);

export const InvestorTagScalarFieldEnumSchema = z.enum(['id','organizationId','companyFilterId','investorCode','tagName','tagCategory','tagMetadata','modifiedAt']);

export const InvestorContactScalarFieldEnumSchema = z.enum(['contactId','organizationId','name','phoneNumber','email','address','remarks','createdAt','updatedAt','createdBy','updatedBy']);

export const FundBasicInfoScalarFieldEnumSchema = z.enum(['fundCode','fundName','investmentType','establishmentDate','trackingIndexName','managementCompanyName','managerCode1st','managerName1st','managerCode2nd','managerName2nd','relatedFundCode','netAssetValue','unitNetValue','closingPrice','managementFeeRate','priceChangePct','custodyFeeRate','tradingVolume','turnoverRate','premiumDiscountRate','tableUpdatedAt']);

export const FundPerformanceReturnScalarFieldEnumSchema = z.enum(['fundCode','fundName','latestDate','previousDate','previousUnitNetValue','ytdTotalReturn','week1TotalReturn','month1TotalReturn','month3TotalReturn','month6TotalReturn','year1TotalReturn','year2TotalReturn','year3TotalReturn','year5TotalReturn','sinceInceptionReturn','tableUpdatedAt']);

export const FundIndustryAllocationScalarFieldEnumSchema = z.enum(['fundCode','fundName','topIndustry1','topIndustry2','topIndustry3','topIndustry4','topIndustry5','topIndustry6','topIndustry7','topIndustry8','topIndustry9','topIndustry10','industryRatio1','industryRatio2','industryRatio3','industryRatio4','industryRatio5','industryRatio6','industryRatio7','industryRatio8','industryRatio9','industryRatio10','tableUpdatedAt']);

export const FundTopHoldingsStockScalarFieldEnumSchema = z.enum(['fundCode','fundName','stockCode1','stockCode2','stockCode3','stockCode4','stockCode5','stockCode6','stockCode7','stockCode8','stockCode9','stockCode10','stockName1','stockName2','stockName3','stockName4','stockName5','stockName6','stockName7','stockName8','stockName9','stockName10','stockMarketValue1','stockMarketValue2','stockMarketValue3','stockMarketValue4','stockMarketValue5','stockMarketValue6','stockMarketValue7','stockMarketValue8','stockMarketValue9','stockMarketValue10','stockRatio1','stockRatio2','stockRatio3','stockRatio4','stockRatio5','stockRatio6','stockRatio7','stockRatio8','stockRatio9','stockRatio10','stockQuantity1','stockQuantity2','stockQuantity3','stockQuantity4','stockQuantity5','stockQuantity6','stockQuantity7','stockQuantity8','stockQuantity9','stockQuantity10','bondCode1','bondCode2','bondCode3','bondCode4','bondCode5','bondName1','bondName2','bondName3','bondName4','bondName5','bondMarketValue1','bondMarketValue2','bondMarketValue3','bondMarketValue4','bondMarketValue5','bondRatio1','bondRatio2','bondRatio3','bondRatio4','bondRatio5','bondQuantity1','bondQuantity2','bondQuantity3','bondQuantity4','bondQuantity5','tableUpdatedAt']);

export const FundManagerBasicInfoScalarFieldEnumSchema = z.enum(['fundCode','fundName','managerName1st','managerCode1st','gender1st','education1st','tenureDays1st','tenureYears1st','historicalFundCount1st','totalFundScale1st','tenureDaysDuplicate1st','currentFundCount1st','awardCount1st','resume1st','managerName2nd','managerCode2nd','gender2nd','education2nd','tenureDays2nd','tenureYears2nd','historicalFundCount2nd','totalFundScale2nd','tenureDaysDuplicate2nd','currentFundCount2nd','awardCount2nd','resume2nd','tableUpdatedAt']);

export const FundManagerPerformanceReturnScalarFieldEnumSchema = z.enum(['managerName','managedFundType','ytdTotalReturn','month1TotalReturn','month3TotalReturn','month6TotalReturn','year1TotalReturn','year2TotalReturn','year3TotalReturn','geometricAnnualReturn','fundCompany','tableUpdatedAt']);

export const FundManagerManagedFundsScalarFieldEnumSchema = z.enum(['id','managerName','fundCode','fundName','startDate','investmentType','fundCompany','tableUpdatedAt']);

export const FundManagementCompanyBasicInfoScalarFieldEnumSchema = z.enum(['fundCode','fundName','companyName','establishmentDate','registeredCapital','generalManager','totalNetAssetValue','fundCount','managerCount','companyPhone','tableUpdatedAt']);

export const FundManagementCustodyScalarFieldEnumSchema = z.enum(['fundCode','fundName','fundFullName','custodian','managementCompanyName','tableUpdatedAt']);

export const StockIndustryClassificationScalarFieldEnumSchema = z.enum(['stockCode','stockName','csrcIndustryNew','thsIndustryNew','swIndustry','tableUpdatedAt']);

export const SortOrderSchema = z.enum(['asc','desc']);

export const NullableJsonNullValueInputSchema = z.enum(['DbNull','JsonNull',]).transform((value) => value === 'JsonNull' ? Prisma.JsonNull : value === 'DbNull' ? Prisma.DbNull : value);

export const QueryModeSchema = z.enum(['default','insensitive']);

export const NullsOrderSchema = z.enum(['first','last']);

export const JsonNullValueFilterSchema = z.enum(['DbNull','JsonNull','AnyNull',]).transform((value) => value === 'JsonNull' ? Prisma.JsonNull : value === 'DbNull' ? Prisma.JsonNull : value === 'AnyNull' ? Prisma.AnyNull : value);

export const PurchaseTypeSchema = z.enum(['SUBSCRIPTION','ONE_TIME']);

export type PurchaseTypeType = `${z.infer<typeof PurchaseTypeSchema>}`

/////////////////////////////////////////
// MODELS
/////////////////////////////////////////

/////////////////////////////////////////
// USER SCHEMA
/////////////////////////////////////////

export const UserSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string(),
  emailVerified: z.boolean(),
  image: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
  username: z.string().nullable(),
  role: z.string().nullable(),
  banned: z.boolean().nullable(),
  banReason: z.string().nullable(),
  banExpires: z.coerce.date().nullable(),
  onboardingComplete: z.boolean(),
  paymentsCustomerId: z.string().nullable(),
  locale: z.string().nullable(),
})

export type User = z.infer<typeof UserSchema>

/////////////////////////////////////////
// SESSION SCHEMA
/////////////////////////////////////////

export const SessionSchema = z.object({
  id: z.string(),
  expiresAt: z.coerce.date(),
  ipAddress: z.string().nullable(),
  userAgent: z.string().nullable(),
  userId: z.string(),
  impersonatedBy: z.string().nullable(),
  activeOrganizationId: z.string().nullable(),
  token: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Session = z.infer<typeof SessionSchema>

/////////////////////////////////////////
// ACCOUNT SCHEMA
/////////////////////////////////////////

export const AccountSchema = z.object({
  id: z.string(),
  accountId: z.string(),
  providerId: z.string(),
  userId: z.string(),
  accessToken: z.string().nullable(),
  refreshToken: z.string().nullable(),
  idToken: z.string().nullable(),
  expiresAt: z.coerce.date().nullable(),
  password: z.string().nullable(),
  accessTokenExpiresAt: z.coerce.date().nullable(),
  refreshTokenExpiresAt: z.coerce.date().nullable(),
  scope: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Account = z.infer<typeof AccountSchema>

/////////////////////////////////////////
// VERIFICATION SCHEMA
/////////////////////////////////////////

export const VerificationSchema = z.object({
  id: z.string(),
  identifier: z.string(),
  value: z.string(),
  expiresAt: z.coerce.date(),
  createdAt: z.coerce.date().nullable(),
  updatedAt: z.coerce.date().nullable(),
})

export type Verification = z.infer<typeof VerificationSchema>

/////////////////////////////////////////
// PASSKEY SCHEMA
/////////////////////////////////////////

export const PasskeySchema = z.object({
  id: z.string(),
  name: z.string().nullable(),
  publicKey: z.string(),
  userId: z.string(),
  credentialID: z.string(),
  counter: z.number().int(),
  deviceType: z.string(),
  backedUp: z.boolean(),
  transports: z.string().nullable(),
  createdAt: z.coerce.date().nullable(),
})

export type Passkey = z.infer<typeof PasskeySchema>

/////////////////////////////////////////
// ORGANIZATION SCHEMA
/////////////////////////////////////////

export const OrganizationSchema = z.object({
  id: z.string(),
  name: z.string(),
  slug: z.string().nullable(),
  logo: z.string().nullable(),
  createdAt: z.coerce.date(),
  metadata: z.string().nullable(),
  paymentsCustomerId: z.string().nullable(),
})

export type Organization = z.infer<typeof OrganizationSchema>

/////////////////////////////////////////
// MEMBER SCHEMA
/////////////////////////////////////////

export const MemberSchema = z.object({
  id: z.string(),
  organizationId: z.string(),
  userId: z.string(),
  role: z.string(),
  createdAt: z.coerce.date(),
})

export type Member = z.infer<typeof MemberSchema>

/////////////////////////////////////////
// INVITATION SCHEMA
/////////////////////////////////////////

export const InvitationSchema = z.object({
  id: z.string(),
  organizationId: z.string(),
  email: z.string(),
  role: z.string().nullable(),
  status: z.string(),
  expiresAt: z.coerce.date(),
  inviterId: z.string(),
})

export type Invitation = z.infer<typeof InvitationSchema>

/////////////////////////////////////////
// PURCHASE SCHEMA
/////////////////////////////////////////

export const PurchaseSchema = z.object({
  type: PurchaseTypeSchema,
  id: z.string().cuid(),
  organizationId: z.string().nullable(),
  userId: z.string().nullable(),
  customerId: z.string(),
  subscriptionId: z.string().nullable(),
  productId: z.string(),
  status: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type Purchase = z.infer<typeof PurchaseSchema>

/////////////////////////////////////////
// AI CHAT SCHEMA
/////////////////////////////////////////

export const AiChatSchema = z.object({
  id: z.string().cuid(),
  organizationId: z.string().nullable(),
  userId: z.string().nullable(),
  title: z.string().nullable(),
  /**
   * [AIChatMessages]
   */
  messages: JsonValueSchema.nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type AiChat = z.infer<typeof AiChatSchema>

/////////////////////////////////////////
// SHAREHOLDER REGISTRY SCHEMA
/////////////////////////////////////////

export const ShareholderRegistrySchema = z.object({
  id: z.string().cuid(),
  fileName: z.string(),
  recordCount: z.number().int(),
  registerDate: z.coerce.date(),
  companyCode: z.string(),
  organizationId: z.string(),
  userId: z.string(),
  uploadedAt: z.coerce.date(),
})

export type ShareholderRegistry = z.infer<typeof ShareholderRegistrySchema>

/////////////////////////////////////////
// COMPANY INFO SCHEMA
/////////////////////////////////////////

export const CompanyInfoSchema = z.object({
  id: z.string().cuid(),
  registryId: z.string(),
  organizationId: z.string(),
  companyCode: z.string(),
  companyName: z.string(),
  registerDate: z.coerce.date(),
  totalShareholders: z.number().int(),
  totalInstitutions: z.number().int(),
  largeShareholdersCount: z.number().int(),
  largeSharesCount: z.instanceof(Prisma.Decimal, { message: "Field 'largeSharesCount' must be a Decimal. Location: ['Models', 'CompanyInfo']"}),
  totalShares: z.instanceof(Prisma.Decimal, { message: "Field 'totalShares' must be a Decimal. Location: ['Models', 'CompanyInfo']"}),
  institutionShares: z.instanceof(Prisma.Decimal, { message: "Field 'institutionShares' must be a Decimal. Location: ['Models', 'CompanyInfo']"}),
  marginAccounts: z.number().int().nullable(),
  marginShares: z.instanceof(Prisma.Decimal, { message: "Field 'marginShares' must be a Decimal. Location: ['Models', 'CompanyInfo']"}).nullable(),
  uploadedAt: z.coerce.date(),
})

export type CompanyInfo = z.infer<typeof CompanyInfoSchema>

/////////////////////////////////////////
// SHAREHOLDER SCHEMA
/////////////////////////////////////////

export const ShareholderSchema = z.object({
  id: z.string().cuid(),
  shareholderId: z.string(),
  registryId: z.string(),
  organizationId: z.string(),
  unifiedAccountNumber: z.string(),
  securitiesAccountName: z.string(),
  shareholderCategory: z.string(),
  numberOfShares: z.instanceof(Prisma.Decimal, { message: "Field 'numberOfShares' must be a Decimal. Location: ['Models', 'Shareholder']"}),
  lockedUpShares: z.instanceof(Prisma.Decimal, { message: "Field 'lockedUpShares' must be a Decimal. Location: ['Models', 'Shareholder']"}),
  shareholdingRatio: z.instanceof(Prisma.Decimal, { message: "Field 'shareholdingRatio' must be a Decimal. Location: ['Models', 'Shareholder']"}),
  frozenShares: z.instanceof(Prisma.Decimal, { message: "Field 'frozenShares' must be a Decimal. Location: ['Models', 'Shareholder']"}),
  cashAccount: z.string().nullable(),
  sharesInCashAccount: z.instanceof(Prisma.Decimal, { message: "Field 'sharesInCashAccount' must be a Decimal. Location: ['Models', 'Shareholder']"}).nullable(),
  marginAccount: z.string().nullable(),
  sharesInMarginAccount: z.instanceof(Prisma.Decimal, { message: "Field 'sharesInMarginAccount' must be a Decimal. Location: ['Models', 'Shareholder']"}).nullable(),
  contactAddress: z.string().nullable(),
  contactNumber: z.string().nullable(),
  zipCode: z.string().nullable(),
  relatedPartyIndicator: z.string().nullable(),
  clientCategory: z.string().nullable(),
  marginCollateralAccountNumber: z.string().nullable(),
  marginCollateralAccountName: z.string().nullable(),
  natureOfShares: z.string().nullable(),
  shareTradingCategory: z.string().nullable(),
  rightsCategory: z.string().nullable(),
  remarks: z.string().nullable(),
  shareholderType: z.string().nullable(),
  registerDate: z.coerce.date(),
  uploadedAt: z.coerce.date(),
})

export type Shareholder = z.infer<typeof ShareholderSchema>

/////////////////////////////////////////
// SHAREHOLDER CLASSIFICATION RULE SCHEMA
/////////////////////////////////////////

/**
 * *
 * * 股东分类规则表（包含匹配字段类型）
 * *
 * * <AUTHOR>
 * * @time 2025-06-26 14:18:31
 * * @description 用于存储股东分类规则，支持基于优先级的股东自动分类
 * * @update 2025-06-26 14:18:31 hayden 添加matchField字段，支持三种匹配方式
 */
export const ShareholderClassificationRuleSchema = z.object({
  id: z.string().cuid(),
  priority: z.number().int(),
  type: z.string(),
  rule: z.string(),
  matchField: z.string(),
  updatedAt: z.coerce.date(),
})

export type ShareholderClassificationRule = z.infer<typeof ShareholderClassificationRuleSchema>

/////////////////////////////////////////
// COMPANY FILTER SCHEMA
/////////////////////////////////////////

/**
 * *
 * * 公司筛选表 - 存储组织级别的本司和对标公司代码配置
 * * <AUTHOR>
 * * @created 2025-07-09 17:06:43
 * * @updated 2025-07-09 17:06:43 hayden 根据投资人管理模块数据库设计方案添加公司筛选表
 * * @description 组织级别的公司代码配置，用于标签系统的基础数据，与投资人标签表建立关联关系
 */
export const CompanyFilterSchema = z.object({
  id: z.string().cuid(),
  organizationId: z.string(),
  companyCode: z.string(),
  benchmarkCompanyCodes: z.string(),
  modifiedAt: z.coerce.date(),
})

export type CompanyFilter = z.infer<typeof CompanyFilterSchema>

/////////////////////////////////////////
// INVESTOR TAG SCHEMA
/////////////////////////////////////////

/**
 * *
 * * 投资人标签表 - 通用标签系统
 * * <AUTHOR>
 * * @created 2025-07-09 17:06:43
 * * @updated 2025-07-09 17:06:43 hayden 根据投资人管理模块数据库设计方案添加投资人标签表
 * * @description 支持系统自动标签（持仓类型）和用户操作标签（收藏）的统一管理，与公司筛选表建立关联
 */
export const InvestorTagSchema = z.object({
  id: z.string().cuid(),
  organizationId: z.string(),
  companyFilterId: z.string().nullable(),
  investorCode: z.string(),
  tagName: z.string(),
  tagCategory: z.string(),
  tagMetadata: JsonValueSchema.nullable(),
  modifiedAt: z.coerce.date(),
})

export type InvestorTag = z.infer<typeof InvestorTagSchema>

/////////////////////////////////////////
// INVESTOR CONTACT SCHEMA
/////////////////////////////////////////

/**
 * *
 * * 投资人联系人表 - 存储投资人联系人信息
 * * <AUTHOR>
 * * @created 2025-07-09 17:06:43
 * * @updated 2025-07-09 17:06:43 hayden 根据投资人管理模块数据库设计方案添加投资人联系人表
 * * @description 组织级别的投资人联系人管理，支持联系人信息的增删改查
 */
export const InvestorContactSchema = z.object({
  contactId: z.string().cuid(),
  organizationId: z.string(),
  name: z.string(),
  phoneNumber: z.string().nullable(),
  email: z.string().nullable(),
  address: z.string().nullable(),
  remarks: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
  createdBy: z.string().nullable(),
  updatedBy: z.string().nullable(),
})

export type InvestorContact = z.infer<typeof InvestorContactSchema>

/////////////////////////////////////////
// FUND BASIC INFO SCHEMA
/////////////////////////////////////////

/**
 * *
 * * 基金基本信息表
 * * 存储基金的核心基础信息，作为整个基金数据体系的主表
 * * 通过基金代码与其他业务表建立关联关系
 * *
 * * <AUTHOR>
 * * @created 2025-01-27 16:47:31
 * * @updated 2025-01-27 16:47:31
 * * @description 基金基本信息的核心存储表，包含基金代码、名称、投资类型、成立日期、净值、费率等关键信息
 */
export const FundBasicInfoSchema = z.object({
  /**
   * 基金代码
   */
  fundCode: z.string(),
  /**
   * 基金名称
   */
  fundName: z.string(),
  /**
   * 投资类型（二级分类）
   */
  investmentType: z.string().nullable(),
  /**
   * 基金成立日
   */
  establishmentDate: z.coerce.date().nullable(),
  /**
   * 跟踪指数名称
   */
  trackingIndexName: z.string().nullable(),
  /**
   * 基金管理人中文名称
   */
  managementCompanyName: z.string().nullable(),
  /**
   * 基金经理代码[任职状态]现任[任职年化回报排名]第1名
   */
  managerCode1st: z.string().nullable(),
  /**
   * 姓名[任职状态]现任[任职年化回报排名]第1名
   */
  managerName1st: z.string().nullable(),
  /**
   * 基金经理代码[任职状态]现任[任职年化回报排名]第2名
   */
  managerCode2nd: z.string().nullable(),
  /**
   * 姓名[任职状态]现任[任职年化回报排名]第2名
   */
  managerName2nd: z.string().nullable(),
  /**
   * 关联基金代码
   */
  relatedFundCode: z.string().nullable(),
  /**
   * 基金资产净值(亿元)
   */
  netAssetValue: z.instanceof(Prisma.Decimal, { message: "Field 'netAssetValue' must be a Decimal. Location: ['Models', 'FundBasicInfo']"}).nullable(),
  /**
   * 单位净值(元)
   */
  unitNetValue: z.instanceof(Prisma.Decimal, { message: "Field 'unitNetValue' must be a Decimal. Location: ['Models', 'FundBasicInfo']"}).nullable(),
  /**
   * 收盘价(元)
   */
  closingPrice: z.instanceof(Prisma.Decimal, { message: "Field 'closingPrice' must be a Decimal. Location: ['Models', 'FundBasicInfo']"}).nullable(),
  /**
   * 管理费率(%)
   */
  managementFeeRate: z.instanceof(Prisma.Decimal, { message: "Field 'managementFeeRate' must be a Decimal. Location: ['Models', 'FundBasicInfo']"}).nullable(),
  /**
   * 涨跌幅(%)
   */
  priceChangePct: z.instanceof(Prisma.Decimal, { message: "Field 'priceChangePct' must be a Decimal. Location: ['Models', 'FundBasicInfo']"}).nullable(),
  /**
   * 托管费率(%)
   */
  custodyFeeRate: z.instanceof(Prisma.Decimal, { message: "Field 'custodyFeeRate' must be a Decimal. Location: ['Models', 'FundBasicInfo']"}).nullable(),
  /**
   * 成交额(元)
   */
  tradingVolume: z.bigint().nullable(),
  /**
   * 换手率(%)
   */
  turnoverRate: z.instanceof(Prisma.Decimal, { message: "Field 'turnoverRate' must be a Decimal. Location: ['Models', 'FundBasicInfo']"}).nullable(),
  /**
   * 相对净值折溢价率(%)
   */
  premiumDiscountRate: z.instanceof(Prisma.Decimal, { message: "Field 'premiumDiscountRate' must be a Decimal. Location: ['Models', 'FundBasicInfo']"}).nullable(),
  /**
   * 表更新时间
   */
  tableUpdatedAt: z.coerce.date(),
})

export type FundBasicInfo = z.infer<typeof FundBasicInfoSchema>

/////////////////////////////////////////
// FUND PERFORMANCE RETURN SCHEMA
/////////////////////////////////////////

/**
 * *
 * * 基金业绩回报表
 * * 存储基金在不同时间周期的收益率表现数据
 * * 用于分析基金的历史业绩和投资回报情况
 * *
 * * <AUTHOR>
 * * @created 2025-01-27 16:47:31
 * * @updated 2025-01-27 16:47:31
 * * @description 基金业绩回报数据表，包含今年以来、近期各时间段的总回报率统计
 */
export const FundPerformanceReturnSchema = z.object({
  /**
   * 基金代码
   */
  fundCode: z.string(),
  /**
   * 基金简称
   */
  fundName: z.string(),
  /**
   * 最新日期
   */
  latestDate: z.coerce.date().nullable(),
  /**
   * 上期日期
   */
  previousDate: z.coerce.date().nullable(),
  /**
   * 上期单位净值
   */
  previousUnitNetValue: z.instanceof(Prisma.Decimal, { message: "Field 'previousUnitNetValue' must be a Decimal. Location: ['Models', 'FundPerformanceReturn']"}).nullable(),
  /**
   * 今年以来总回报(%)
   */
  ytdTotalReturn: z.instanceof(Prisma.Decimal, { message: "Field 'ytdTotalReturn' must be a Decimal. Location: ['Models', 'FundPerformanceReturn']"}).nullable(),
  /**
   * 最近一周总回报(%)
   */
  week1TotalReturn: z.instanceof(Prisma.Decimal, { message: "Field 'week1TotalReturn' must be a Decimal. Location: ['Models', 'FundPerformanceReturn']"}).nullable(),
  /**
   * 最近一月总回报(%)
   */
  month1TotalReturn: z.instanceof(Prisma.Decimal, { message: "Field 'month1TotalReturn' must be a Decimal. Location: ['Models', 'FundPerformanceReturn']"}).nullable(),
  /**
   * 最近三月总回报(%)
   */
  month3TotalReturn: z.instanceof(Prisma.Decimal, { message: "Field 'month3TotalReturn' must be a Decimal. Location: ['Models', 'FundPerformanceReturn']"}).nullable(),
  /**
   * 最近六月总回报(%)
   */
  month6TotalReturn: z.instanceof(Prisma.Decimal, { message: "Field 'month6TotalReturn' must be a Decimal. Location: ['Models', 'FundPerformanceReturn']"}).nullable(),
  /**
   * 最近一年总回报(%)
   */
  year1TotalReturn: z.instanceof(Prisma.Decimal, { message: "Field 'year1TotalReturn' must be a Decimal. Location: ['Models', 'FundPerformanceReturn']"}).nullable(),
  /**
   * 最近两年总回报(%)
   */
  year2TotalReturn: z.instanceof(Prisma.Decimal, { message: "Field 'year2TotalReturn' must be a Decimal. Location: ['Models', 'FundPerformanceReturn']"}).nullable(),
  /**
   * 最近三年总回报(%)
   */
  year3TotalReturn: z.instanceof(Prisma.Decimal, { message: "Field 'year3TotalReturn' must be a Decimal. Location: ['Models', 'FundPerformanceReturn']"}).nullable(),
  /**
   * 最近五年总回报(%)
   */
  year5TotalReturn: z.instanceof(Prisma.Decimal, { message: "Field 'year5TotalReturn' must be a Decimal. Location: ['Models', 'FundPerformanceReturn']"}).nullable(),
  /**
   * 成立以来总回报(%)
   */
  sinceInceptionReturn: z.instanceof(Prisma.Decimal, { message: "Field 'sinceInceptionReturn' must be a Decimal. Location: ['Models', 'FundPerformanceReturn']"}).nullable(),
  /**
   * 表更新时间
   */
  tableUpdatedAt: z.coerce.date(),
})

export type FundPerformanceReturn = z.infer<typeof FundPerformanceReturnSchema>

/////////////////////////////////////////
// FUND INDUSTRY ALLOCATION SCHEMA
/////////////////////////////////////////

/**
 * *
 * * 基金行业配置表
 * * 存储基金的重仓行业分布情况和配置比例
 * * 用于分析基金的行业投资偏好和风险分散情况
 * *
 * * <AUTHOR>
 * * @created 2025-01-27 16:47:31
 * * @updated 2025-01-27 16:47:31
 * * @description 基金行业配置数据表，包含前10大重仓行业及其占基金资产净值的比例
 */
export const FundIndustryAllocationSchema = z.object({
  /**
   * 证券代码
   */
  fundCode: z.string(),
  /**
   * 证券名称
   */
  fundName: z.string(),
  /**
   * 重仓行业名称[排名]第1名
   */
  topIndustry1: z.string().nullable(),
  /**
   * 重仓行业名称[排名]第2名
   */
  topIndustry2: z.string().nullable(),
  /**
   * 重仓行业名称[排名]第3名
   */
  topIndustry3: z.string().nullable(),
  /**
   * 重仓行业名称[排名]第4名
   */
  topIndustry4: z.string().nullable(),
  /**
   * 重仓行业名称[排名]第5名
   */
  topIndustry5: z.string().nullable(),
  /**
   * 重仓行业名称[排名]第6名
   */
  topIndustry6: z.string().nullable(),
  /**
   * 重仓行业名称[排名]第7名
   */
  topIndustry7: z.string().nullable(),
  /**
   * 重仓行业名称[排名]第8名
   */
  topIndustry8: z.string().nullable(),
  /**
   * 重仓行业名称[排名]第9名
   */
  topIndustry9: z.string().nullable(),
  /**
   * 重仓行业名称[排名]第10名
   */
  topIndustry10: z.string().nullable(),
  /**
   * 重仓行业市值占基金资产净值比[排名]第1名[单位]%
   */
  industryRatio1: z.instanceof(Prisma.Decimal, { message: "Field 'industryRatio1' must be a Decimal. Location: ['Models', 'FundIndustryAllocation']"}).nullable(),
  /**
   * 重仓行业市值占基金资产净值比[排名]第2名[单位]%
   */
  industryRatio2: z.instanceof(Prisma.Decimal, { message: "Field 'industryRatio2' must be a Decimal. Location: ['Models', 'FundIndustryAllocation']"}).nullable(),
  /**
   * 重仓行业市值占基金资产净值比[排名]第3名[单位]%
   */
  industryRatio3: z.instanceof(Prisma.Decimal, { message: "Field 'industryRatio3' must be a Decimal. Location: ['Models', 'FundIndustryAllocation']"}).nullable(),
  /**
   * 重仓行业市值占基金资产净值比[排名]第4名[单位]%
   */
  industryRatio4: z.instanceof(Prisma.Decimal, { message: "Field 'industryRatio4' must be a Decimal. Location: ['Models', 'FundIndustryAllocation']"}).nullable(),
  /**
   * 重仓行业市值占基金资产净值比[排名]第5名[单位]%
   */
  industryRatio5: z.instanceof(Prisma.Decimal, { message: "Field 'industryRatio5' must be a Decimal. Location: ['Models', 'FundIndustryAllocation']"}).nullable(),
  /**
   * 重仓行业市值占基金资产净值比[排名]第6名[单位]%
   */
  industryRatio6: z.instanceof(Prisma.Decimal, { message: "Field 'industryRatio6' must be a Decimal. Location: ['Models', 'FundIndustryAllocation']"}).nullable(),
  /**
   * 重仓行业市值占基金资产净值比[排名]第7名[单位]%
   */
  industryRatio7: z.instanceof(Prisma.Decimal, { message: "Field 'industryRatio7' must be a Decimal. Location: ['Models', 'FundIndustryAllocation']"}).nullable(),
  /**
   * 重仓行业市值占基金资产净值比[排名]第8名[单位]%
   */
  industryRatio8: z.instanceof(Prisma.Decimal, { message: "Field 'industryRatio8' must be a Decimal. Location: ['Models', 'FundIndustryAllocation']"}).nullable(),
  /**
   * 重仓行业市值占基金资产净值比[排名]第9名[单位]%
   */
  industryRatio9: z.instanceof(Prisma.Decimal, { message: "Field 'industryRatio9' must be a Decimal. Location: ['Models', 'FundIndustryAllocation']"}).nullable(),
  /**
   * 重仓行业市值占基金资产净值比[排名]第10名[单位]%
   */
  industryRatio10: z.instanceof(Prisma.Decimal, { message: "Field 'industryRatio10' must be a Decimal. Location: ['Models', 'FundIndustryAllocation']"}).nullable(),
  /**
   * 表更新时间
   */
  tableUpdatedAt: z.coerce.date(),
})

export type FundIndustryAllocation = z.infer<typeof FundIndustryAllocationSchema>

/////////////////////////////////////////
// FUND TOP HOLDINGS STOCK SCHEMA
/////////////////////////////////////////

/**
 * *
 * * 基金重仓股票和债券表
 * * 存储基金的重仓股票和债券持仓明细信息
 * * 包含持仓代码、名称、市值、占比、数量等详细数据
 * *
 * * <AUTHOR>
 * * @created 2025-01-27 16:47:31
 * * @updated 2025-01-27 16:47:31
 * * @description 基金重仓持仓数据表，包含前10大重仓股票和前5大重仓债券的详细持仓信息
 */
export const FundTopHoldingsStockSchema = z.object({
  /**
   * 证券代码
   */
  fundCode: z.string(),
  /**
   * 证券名称
   */
  fundName: z.string(),
  /**
   * 重仓证券代码[排名]第1名
   */
  stockCode1: z.string().nullable(),
  /**
   * 重仓证券代码[排名]第2名
   */
  stockCode2: z.string().nullable(),
  /**
   * 重仓证券代码[排名]第3名
   */
  stockCode3: z.string().nullable(),
  /**
   * 重仓证券代码[排名]第4名
   */
  stockCode4: z.string().nullable(),
  /**
   * 重仓证券代码[排名]第5名
   */
  stockCode5: z.string().nullable(),
  /**
   * 重仓证券代码[排名]第6名
   */
  stockCode6: z.string().nullable(),
  /**
   * 重仓证券代码[排名]第7名
   */
  stockCode7: z.string().nullable(),
  /**
   * 重仓证券代码[排名]第8名
   */
  stockCode8: z.string().nullable(),
  /**
   * 重仓证券代码[排名]第9名
   */
  stockCode9: z.string().nullable(),
  /**
   * 重仓证券代码[排名]第10名
   */
  stockCode10: z.string().nullable(),
  /**
   * 重仓证券名称[排名]第1名
   */
  stockName1: z.string().nullable(),
  /**
   * 重仓证券名称[排名]第2名
   */
  stockName2: z.string().nullable(),
  /**
   * 重仓证券名称[排名]第3名
   */
  stockName3: z.string().nullable(),
  /**
   * 重仓证券名称[排名]第4名
   */
  stockName4: z.string().nullable(),
  /**
   * 重仓证券名称[排名]第5名
   */
  stockName5: z.string().nullable(),
  /**
   * 重仓证券名称[排名]第6名
   */
  stockName6: z.string().nullable(),
  /**
   * 重仓证券名称[排名]第7名
   */
  stockName7: z.string().nullable(),
  /**
   * 重仓证券名称[排名]第8名
   */
  stockName8: z.string().nullable(),
  /**
   * 重仓证券名称[排名]第9名
   */
  stockName9: z.string().nullable(),
  /**
   * 重仓证券名称[排名]第10名
   */
  stockName10: z.string().nullable(),
  /**
   * 重仓证券持仓市值[排名]第1名[单位]元
   */
  stockMarketValue1: z.bigint().nullable(),
  /**
   * 重仓证券持仓市值[排名]第2名[单位]元
   */
  stockMarketValue2: z.bigint().nullable(),
  /**
   * 重仓证券持仓市值[排名]第3名[单位]元
   */
  stockMarketValue3: z.bigint().nullable(),
  /**
   * 重仓证券持仓市值[排名]第4名[单位]元
   */
  stockMarketValue4: z.bigint().nullable(),
  /**
   * 重仓证券持仓市值[排名]第5名[单位]元
   */
  stockMarketValue5: z.bigint().nullable(),
  /**
   * 重仓证券持仓市值[排名]第6名[单位]元
   */
  stockMarketValue6: z.bigint().nullable(),
  /**
   * 重仓证券持仓市值[排名]第7名[单位]元
   */
  stockMarketValue7: z.bigint().nullable(),
  /**
   * 重仓证券持仓市值[排名]第8名[单位]元
   */
  stockMarketValue8: z.bigint().nullable(),
  /**
   * 重仓证券持仓市值[排名]第9名[单位]元
   */
  stockMarketValue9: z.bigint().nullable(),
  /**
   * 重仓证券持仓市值[排名]第10名[单位]元
   */
  stockMarketValue10: z.bigint().nullable(),
  /**
   * 重仓证券市值占基金资产净值比[排名]第1名[单位]%
   */
  stockRatio1: z.instanceof(Prisma.Decimal, { message: "Field 'stockRatio1' must be a Decimal. Location: ['Models', 'FundTopHoldingsStock']"}).nullable(),
  /**
   * 重仓证券市值占基金资产净值比[排名]第2名[单位]%
   */
  stockRatio2: z.instanceof(Prisma.Decimal, { message: "Field 'stockRatio2' must be a Decimal. Location: ['Models', 'FundTopHoldingsStock']"}).nullable(),
  /**
   * 重仓证券市值占基金资产净值比[排名]第3名[单位]%
   */
  stockRatio3: z.instanceof(Prisma.Decimal, { message: "Field 'stockRatio3' must be a Decimal. Location: ['Models', 'FundTopHoldingsStock']"}).nullable(),
  /**
   * 重仓证券市值占基金资产净值比[排名]第4名[单位]%
   */
  stockRatio4: z.instanceof(Prisma.Decimal, { message: "Field 'stockRatio4' must be a Decimal. Location: ['Models', 'FundTopHoldingsStock']"}).nullable(),
  /**
   * 重仓证券市值占基金资产净值比[排名]第5名[单位]%
   */
  stockRatio5: z.instanceof(Prisma.Decimal, { message: "Field 'stockRatio5' must be a Decimal. Location: ['Models', 'FundTopHoldingsStock']"}).nullable(),
  /**
   * 重仓证券市值占基金资产净值比[排名]第6名[单位]%
   */
  stockRatio6: z.instanceof(Prisma.Decimal, { message: "Field 'stockRatio6' must be a Decimal. Location: ['Models', 'FundTopHoldingsStock']"}).nullable(),
  /**
   * 重仓证券市值占基金资产净值比[排名]第7名[单位]%
   */
  stockRatio7: z.instanceof(Prisma.Decimal, { message: "Field 'stockRatio7' must be a Decimal. Location: ['Models', 'FundTopHoldingsStock']"}).nullable(),
  /**
   * 重仓证券市值占基金资产净值比[排名]第8名[单位]%
   */
  stockRatio8: z.instanceof(Prisma.Decimal, { message: "Field 'stockRatio8' must be a Decimal. Location: ['Models', 'FundTopHoldingsStock']"}).nullable(),
  /**
   * 重仓证券市值占基金资产净值比[排名]第9名[单位]%
   */
  stockRatio9: z.instanceof(Prisma.Decimal, { message: "Field 'stockRatio9' must be a Decimal. Location: ['Models', 'FundTopHoldingsStock']"}).nullable(),
  /**
   * 重仓证券市值占基金资产净值比[排名]第10名[单位]%
   */
  stockRatio10: z.instanceof(Prisma.Decimal, { message: "Field 'stockRatio10' must be a Decimal. Location: ['Models', 'FundTopHoldingsStock']"}).nullable(),
  /**
   * 重仓证券持仓数量[排名]第1名[单位]份
   */
  stockQuantity1: z.bigint().nullable(),
  /**
   * 重仓证券持仓数量[排名]第2名[单位]份
   */
  stockQuantity2: z.bigint().nullable(),
  /**
   * 重仓证券持仓数量[排名]第3名[单位]份
   */
  stockQuantity3: z.bigint().nullable(),
  /**
   * 重仓证券持仓数量[排名]第4名[单位]份
   */
  stockQuantity4: z.bigint().nullable(),
  /**
   * 重仓证券持仓数量[排名]第5名[单位]份
   */
  stockQuantity5: z.bigint().nullable(),
  /**
   * 重仓证券持仓数量[排名]第6名[单位]份
   */
  stockQuantity6: z.bigint().nullable(),
  /**
   * 重仓证券持仓数量[排名]第7名[单位]份
   */
  stockQuantity7: z.bigint().nullable(),
  /**
   * 重仓证券持仓数量[排名]第8名[单位]份
   */
  stockQuantity8: z.bigint().nullable(),
  /**
   * 重仓证券持仓数量[排名]第9名[单位]份
   */
  stockQuantity9: z.bigint().nullable(),
  /**
   * 重仓证券持仓数量[排名]第10名[单位]份
   */
  stockQuantity10: z.bigint().nullable(),
  /**
   * 重仓债券代码[排名]第1名
   */
  bondCode1: z.string().nullable(),
  /**
   * 重仓债券代码[排名]第2名
   */
  bondCode2: z.string().nullable(),
  /**
   * 重仓债券代码[排名]第3名
   */
  bondCode3: z.string().nullable(),
  /**
   * 重仓债券代码[排名]第4名
   */
  bondCode4: z.string().nullable(),
  /**
   * 重仓债券代码[排名]第5名
   */
  bondCode5: z.string().nullable(),
  /**
   * 重仓债券名称[排名]第1名
   */
  bondName1: z.string().nullable(),
  /**
   * 重仓债券名称[排名]第2名
   */
  bondName2: z.string().nullable(),
  /**
   * 重仓债券名称[排名]第3名
   */
  bondName3: z.string().nullable(),
  /**
   * 重仓债券名称[排名]第4名
   */
  bondName4: z.string().nullable(),
  /**
   * 重仓债券名称[排名]第5名
   */
  bondName5: z.string().nullable(),
  /**
   * 重仓债券持有市值[排名]第1名[单位]元
   */
  bondMarketValue1: z.bigint().nullable(),
  /**
   * 重仓债券持有市值[排名]第2名[单位]元
   */
  bondMarketValue2: z.bigint().nullable(),
  /**
   * 重仓债券持有市值[排名]第3名[单位]元
   */
  bondMarketValue3: z.bigint().nullable(),
  /**
   * 重仓债券持有市值[排名]第4名[单位]元
   */
  bondMarketValue4: z.bigint().nullable(),
  /**
   * 重仓债券持有市值[排名]第5名[单位]元
   */
  bondMarketValue5: z.bigint().nullable(),
  /**
   * 重仓债券市值占基金资产净值比[排名]第1名[单位]%
   */
  bondRatio1: z.instanceof(Prisma.Decimal, { message: "Field 'bondRatio1' must be a Decimal. Location: ['Models', 'FundTopHoldingsStock']"}).nullable(),
  /**
   * 重仓债券市值占基金资产净值比[排名]第2名[单位]%
   */
  bondRatio2: z.instanceof(Prisma.Decimal, { message: "Field 'bondRatio2' must be a Decimal. Location: ['Models', 'FundTopHoldingsStock']"}).nullable(),
  /**
   * 重仓债券市值占基金资产净值比[排名]第3名[单位]%
   */
  bondRatio3: z.instanceof(Prisma.Decimal, { message: "Field 'bondRatio3' must be a Decimal. Location: ['Models', 'FundTopHoldingsStock']"}).nullable(),
  /**
   * 重仓债券市值占基金资产净值比[排名]第4名[单位]%
   */
  bondRatio4: z.instanceof(Prisma.Decimal, { message: "Field 'bondRatio4' must be a Decimal. Location: ['Models', 'FundTopHoldingsStock']"}).nullable(),
  /**
   * 重仓债券市值占基金资产净值比[排名]第5名[单位]%
   */
  bondRatio5: z.instanceof(Prisma.Decimal, { message: "Field 'bondRatio5' must be a Decimal. Location: ['Models', 'FundTopHoldingsStock']"}).nullable(),
  /**
   * 重仓债券持仓数量[排名]第1名[单位]张
   */
  bondQuantity1: z.bigint().nullable(),
  /**
   * 重仓债券持仓数量[排名]第2名[单位]张
   */
  bondQuantity2: z.bigint().nullable(),
  /**
   * 重仓债券持仓数量[排名]第3名[单位]张
   */
  bondQuantity3: z.bigint().nullable(),
  /**
   * 重仓债券持仓数量[排名]第4名[单位]张
   */
  bondQuantity4: z.bigint().nullable(),
  /**
   * 重仓债券持仓数量[排名]第5名[单位]张
   */
  bondQuantity5: z.bigint().nullable(),
  /**
   * 表更新时间
   */
  tableUpdatedAt: z.coerce.date(),
})

export type FundTopHoldingsStock = z.infer<typeof FundTopHoldingsStockSchema>

/////////////////////////////////////////
// FUND MANAGER BASIC INFO SCHEMA
/////////////////////////////////////////

/**
 * *
 * * 基金经理基本信息表
 * * 存储基金经理的个人基本信息和从业经历
 * * 包含第1名和第2名基金经理的完整信息，包括学历、任职年限、管理规模等关键信息
 * *
 * * <AUTHOR>
 * * @created 2025-01-27 16:47:31
 * * @updated 2025-01-30 16:47:31
 * * @description 基金经理基本信息数据表，包含基金经理的个人资料、从业经历和管理业绩统计，支持双经理管理模式
 */
export const FundManagerBasicInfoSchema = z.object({
  /**
   * 证券代码
   */
  fundCode: z.string(),
  /**
   * 证券名称
   */
  fundName: z.string(),
  /**
   * 姓名[任职状态]现任[任职年化回报排名]第1名
   */
  managerName1st: z.string().nullable(),
  /**
   * 基金经理代码[任职状态]现任[任职年化回报排名]第1名
   */
  managerCode1st: z.string().nullable(),
  /**
   * 性别[任职状态]现任[任职年化回报排名]第1名[是否显示经理名称]否
   */
  gender1st: z.string().nullable(),
  /**
   * 学历[任职状态]现任[任职年化回报排名]第1名[是否显示经理名称]否
   */
  education1st: z.string().nullable(),
  /**
   * 任职天数[任职状态]现任[任职年化回报排名]第1名[是否显示经理名称]否[单位]天
   */
  tenureDays1st: z.number().int().nullable(),
  /**
   * 自然任职年限[任职状态]现任[任职年化回报排名]第1名[是否显示经理名称]否[单位]年
   */
  tenureYears1st: z.instanceof(Prisma.Decimal, { message: "Field 'tenureYears1st' must be a Decimal. Location: ['Models', 'FundManagerBasicInfo']"}).nullable(),
  /**
   * 历任基金数[任职状态]现任[任职年化回报排名]第1名[是否显示经理名称]否[单位]个
   */
  historicalFundCount1st: z.number().int().nullable(),
  /**
   * 任职基金总规模[任职状态]现任[任职年化回报排名]第1名[是否显示经理名称]否[单位设置]万元
   */
  totalFundScale1st: z.instanceof(Prisma.Decimal, { message: "Field 'totalFundScale1st' must be a Decimal. Location: ['Models', 'FundManagerBasicInfo']"}).nullable(),
  /**
   * 任职天数[任职状态]现任[任职年化回报排名]第1名[是否显示经理名称]否[单位]天
   */
  tenureDaysDuplicate1st: z.number().int().nullable(),
  /**
   * 在任基金数[任职状态]现任[任职年化回报排名]第1名[是否显示经理名称]否
   */
  currentFundCount1st: z.number().int().nullable(),
  /**
   * 经理获奖次数[任职状态]现任[任职年化回报排名]第1名[是否显示经理名称]否[单位]次
   */
  awardCount1st: z.number().int().nullable(),
  /**
   * 简历[任职状态]现任[任职年化回报排名]第1名[是否显示经理名称]否
   */
  resume1st: z.string().nullable(),
  /**
   * 姓名[任职状态]现任[任职年化回报排名]第2名
   */
  managerName2nd: z.string().nullable(),
  /**
   * 基金经理代码[任职状态]现任[任职年化回报排名]第2名
   */
  managerCode2nd: z.string().nullable(),
  /**
   * 性别[任职状态]现任[任职年化回报排名]第2名[是否显示经理名称]否
   */
  gender2nd: z.string().nullable(),
  /**
   * 学历[任职状态]现任[任职年化回报排名]第2名[是否显示经理名称]否
   */
  education2nd: z.string().nullable(),
  /**
   * 任职天数[任职状态]现任[任职年化回报排名]第2名[是否显示经理名称]否[单位]天
   */
  tenureDays2nd: z.number().int().nullable(),
  /**
   * 自然任职年限[任职状态]现任[任职年化回报排名]第2名[是否显示经理名称]否[单位]年
   */
  tenureYears2nd: z.instanceof(Prisma.Decimal, { message: "Field 'tenureYears2nd' must be a Decimal. Location: ['Models', 'FundManagerBasicInfo']"}).nullable(),
  /**
   * 历任基金数[任职状态]现任[任职年化回报排名]第2名[是否显示经理名称]否[单位]个
   */
  historicalFundCount2nd: z.number().int().nullable(),
  /**
   * 任职基金总规模[任职状态]现任[任职年化回报排名]第2名[是否显示经理名称]否[单位设置]万元
   */
  totalFundScale2nd: z.instanceof(Prisma.Decimal, { message: "Field 'totalFundScale2nd' must be a Decimal. Location: ['Models', 'FundManagerBasicInfo']"}).nullable(),
  /**
   * 任职天数[任职状态]现任[任职年化回报排名]第2名[是否显示经理名称]否[单位]天
   */
  tenureDaysDuplicate2nd: z.number().int().nullable(),
  /**
   * 在任基金数[任职状态]现任[任职年化回报排名]第2名[是否显示经理名称]否
   */
  currentFundCount2nd: z.number().int().nullable(),
  /**
   * 经理获奖次数[任职状态]现任[任职年化回报排名]第2名[是否显示经理名称]否[单位]次
   */
  awardCount2nd: z.number().int().nullable(),
  /**
   * 简历[任职状态]现任[任职年化回报排名]第2名[是否显示经理名称]否
   */
  resume2nd: z.string().nullable(),
  /**
   * 表更新时间
   */
  tableUpdatedAt: z.coerce.date(),
})

export type FundManagerBasicInfo = z.infer<typeof FundManagerBasicInfoSchema>

/////////////////////////////////////////
// FUND MANAGER PERFORMANCE RETURN SCHEMA
/////////////////////////////////////////

/**
 * *
 * * 基金经理业绩回报表
 * * 存储基金经理在不同时间周期的投资业绩表现
 * * 用于评估基金经理的投资管理能力和历史业绩
 * *
 * * <AUTHOR>
 * * @created 2025-01-27 16:47:31
 * * @updated 2025-01-30 16:47:31
 * * @description 基金经理业绩回报数据表，包含基金经理在各时间段的投资回报率统计
 * * @modification 2025-01-30 16:47:31 - 修改主键策略：将managerName和managedFundType设为复合主键，解决基金经理名称重复问题
 */
export const FundManagerPerformanceReturnSchema = z.object({
  /**
   * 基金经理
   */
  managerName: z.string(),
  /**
   * 在管基金类型
   */
  managedFundType: z.string(),
  /**
   * 今年以来总回报(%)
   */
  ytdTotalReturn: z.instanceof(Prisma.Decimal, { message: "Field 'ytdTotalReturn' must be a Decimal. Location: ['Models', 'FundManagerPerformanceReturn']"}).nullable(),
  /**
   * 最近一月总回报(%)
   */
  month1TotalReturn: z.instanceof(Prisma.Decimal, { message: "Field 'month1TotalReturn' must be a Decimal. Location: ['Models', 'FundManagerPerformanceReturn']"}).nullable(),
  /**
   * 最近三月总回报(%)
   */
  month3TotalReturn: z.instanceof(Prisma.Decimal, { message: "Field 'month3TotalReturn' must be a Decimal. Location: ['Models', 'FundManagerPerformanceReturn']"}).nullable(),
  /**
   * 最近六月总回报(%)
   */
  month6TotalReturn: z.instanceof(Prisma.Decimal, { message: "Field 'month6TotalReturn' must be a Decimal. Location: ['Models', 'FundManagerPerformanceReturn']"}).nullable(),
  /**
   * 最近一年总回报(%)
   */
  year1TotalReturn: z.instanceof(Prisma.Decimal, { message: "Field 'year1TotalReturn' must be a Decimal. Location: ['Models', 'FundManagerPerformanceReturn']"}).nullable(),
  /**
   * 最近两年总回报(%)
   */
  year2TotalReturn: z.instanceof(Prisma.Decimal, { message: "Field 'year2TotalReturn' must be a Decimal. Location: ['Models', 'FundManagerPerformanceReturn']"}).nullable(),
  /**
   * 最近三年总回报(%)
   */
  year3TotalReturn: z.instanceof(Prisma.Decimal, { message: "Field 'year3TotalReturn' must be a Decimal. Location: ['Models', 'FundManagerPerformanceReturn']"}).nullable(),
  /**
   * 几何任职年化回报(%)
   */
  geometricAnnualReturn: z.instanceof(Prisma.Decimal, { message: "Field 'geometricAnnualReturn' must be a Decimal. Location: ['Models', 'FundManagerPerformanceReturn']"}).nullable(),
  /**
   * 基金公司
   */
  fundCompany: z.string().nullable(),
  /**
   * 表更新时间
   */
  tableUpdatedAt: z.coerce.date(),
})

export type FundManagerPerformanceReturn = z.infer<typeof FundManagerPerformanceReturnSchema>

/////////////////////////////////////////
// FUND MANAGER MANAGED FUNDS SCHEMA
/////////////////////////////////////////

/**
 * *
 * * 基金经理管理基金表
 * * 存储基金经理与其管理基金的对应关系
 * * 包含任职起始日期、投资类型等关键信息
 * *
 * * <AUTHOR>
 * * @created 2025-01-27
 * * @description 基金经理管理基金关系表，记录基金经理的基金管理历史和当前管理状态
 */
export const FundManagerManagedFundsSchema = z.object({
  /**
   * 自增主键
   */
  id: z.number().int(),
  /**
   * 基金经理
   */
  managerName: z.string(),
  /**
   * 代码
   */
  fundCode: z.string(),
  /**
   * 名称
   */
  fundName: z.string(),
  /**
   * 任职起始日
   */
  startDate: z.coerce.date().nullable(),
  /**
   * 投资类型
   */
  investmentType: z.string().nullable(),
  /**
   * 基金公司
   */
  fundCompany: z.string().nullable(),
  /**
   * 表更新时间
   */
  tableUpdatedAt: z.coerce.date(),
})

export type FundManagerManagedFunds = z.infer<typeof FundManagerManagedFundsSchema>

/////////////////////////////////////////
// FUND MANAGEMENT COMPANY BASIC INFO SCHEMA
/////////////////////////////////////////

/**
 * *
 * * 基金公司基本信息表
 * * 存储基金管理公司的基本信息和经营数据
 * * 包含公司规模、基金数量、经理人数等关键指标
 * *
 * * <AUTHOR>
 * * @created 2025-01-27 16:47:31
 * * @updated 2025-01-27 16:47:31
 * * @description 基金管理公司基本信息数据表，包含公司成立信息、资产规模、管理团队等核心数据
 */
export const FundManagementCompanyBasicInfoSchema = z.object({
  /**
   * 证券代码
   */
  fundCode: z.string(),
  /**
   * 证券名称
   */
  fundName: z.string(),
  /**
   * 基金管理人中文名称
   */
  companyName: z.string().nullable(),
  /**
   * 基金管理人成立日期
   */
  establishmentDate: z.coerce.date().nullable(),
  /**
   * 基金管理人注册资本
   */
  registeredCapital: z.string().nullable(),
  /**
   * 基金管理人总经理
   */
  generalManager: z.string().nullable(),
  /**
   * 基金管理人资产净值合计[报告期]最新一期(MRQ)[单位]亿元
   */
  totalNetAssetValue: z.instanceof(Prisma.Decimal, { message: "Field 'totalNetAssetValue' must be a Decimal. Location: ['Models', 'FundManagementCompanyBasicInfo']"}).nullable(),
  /**
   * 旗下基金数
   */
  fundCount: z.number().int().nullable(),
  /**
   * 基金经理数
   */
  managerCount: z.number().int().nullable(),
  /**
   * 基金管理人电话
   */
  companyPhone: z.string().nullable(),
  /**
   * 表更新时间
   */
  tableUpdatedAt: z.coerce.date(),
})

export type FundManagementCompanyBasicInfo = z.infer<typeof FundManagementCompanyBasicInfoSchema>

/////////////////////////////////////////
// FUND MANAGEMENT CUSTODY SCHEMA
/////////////////////////////////////////

/**
 * *
 * * 基金管理和托管信息表
 * * 存储基金的管理人和托管人信息
 * * 包含基金代码、名称、全称、托管人、管理人等核心信息
 * *
 * * <AUTHOR>
 * * @created 2025-01-30 16:47:31
 * * @updated 2025-01-30 16:47:31
 * * @description 基金管理和托管信息数据表，记录基金的管理机构和托管机构信息，用于基金监管和风险控制
 */
export const FundManagementCustodySchema = z.object({
  /**
   * 证券代码
   */
  fundCode: z.string(),
  /**
   * 证券名称
   */
  fundName: z.string(),
  /**
   * 基金全称
   */
  fundFullName: z.string().nullable(),
  /**
   * 基金托管人
   */
  custodian: z.string().nullable(),
  /**
   * 基金管理人中文名称
   */
  managementCompanyName: z.string().nullable(),
  /**
   * 表更新时间
   */
  tableUpdatedAt: z.coerce.date(),
})

export type FundManagementCustody = z.infer<typeof FundManagementCustodySchema>

/////////////////////////////////////////
// STOCK INDUSTRY CLASSIFICATION SCHEMA
/////////////////////////////////////////

/**
 * *
 * * 股票行业分类信息表
 * * 存储股票的行业分类信息，包含多种行业分类标准
 * * 以股票代码为唯一主键，包含证监会、同花顺、申万等行业分类
 * *
 * * <AUTHOR>
 * * @created 2025-07-30 16:47:31
 * * @updated 2025-07-30 16:47:31
 * * @description 股票行业分类数据表，记录股票在不同行业分类标准下的归属，用于行业分析和投资研究
 */
export const StockIndustryClassificationSchema = z.object({
  /**
   * 股票代码
   */
  stockCode: z.string(),
  /**
   * 股票名称
   */
  stockName: z.string(),
  /**
   * 证监会行业分类(新)
   */
  csrcIndustryNew: z.string().nullable(),
  /**
   * 同花顺行业分类(新)
   */
  thsIndustryNew: z.string().nullable(),
  /**
   * 申万行业分类
   */
  swIndustry: z.string().nullable(),
  /**
   * 表更新时间
   */
  tableUpdatedAt: z.coerce.date(),
})

export type StockIndustryClassification = z.infer<typeof StockIndustryClassificationSchema>
